import React from 'react';

interface IconProps {
  width?: number;
  height?: number;
  className?: string;
}

export const SoundCloudIcon: React.FC<IconProps> = ({ 
  width = 24, 
  height = 24, 
  className = "" 
}) => (
  <svg 
    width={width} 
    height={height} 
    className={className} 
    viewBox="0 0 24 24" 
    fill="currentColor"
  >
    <path d="M1.175 12.225c-.051 0-.094.046-.101.1-.233 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.127 2.752-2.894 4.906.007.054.05.1.101.1h.001zm2.226 0c-.051 0-.094.046-.101.1-.465 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.359 2.752-2.894 4.906.007.054.05.1.101.1h.001zm2.226 0c-.051 0-.094.046-.101.1-.465 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.359 2.752-2.894 4.906.007.054.05.1.101.1h.001zm2.226 0c-.051 0-.094.046-.101.1-.465 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.359 2.752-2.894 4.906.007.054.05.1.101.1h.001zm8.597 0c-.051 0-.094.046-.101.1-.465 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.359 2.752-2.894 4.906.007.054.05.1.101.1h.001zm2.226 0c-.051 0-.094.046-.101.1-.465 2.154.97 4.227 2.894 4.906.138.049.263-.084.263-.23v-.776c0-.096-.047-.187-.125-.259-.757-.705-1.178-1.717-1.178-2.748 0-.65.22-1.25.585-1.729.088-.115.088-.28 0-.395-.365-.479-.585-1.079-.585-1.729 0-1.031.421-2.043 1.178-2.748.078-.072.125-.163.125-.259v-.776c0-.146-.125-.279-.263-.23-1.924.679-3.359 2.752-2.894 4.906.007.054.05.1.101.1h.001z"/>
  </svg>
);

export const YouTubeIcon: React.FC<IconProps> = ({ 
  width = 24, 
  height = 24, 
  className = "" 
}) => (
  <svg 
    width={width} 
    height={height} 
    className={className} 
    viewBox="0 0 24 24" 
    fill="currentColor"
  >
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
);

export const MusicIcon: React.FC<IconProps> = ({ 
  width = 24, 
  height = 24, 
  className = "" 
}) => (
  <svg 
    width={width} 
    height={height} 
    className={className} 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round"
  >
    <path d="M9 18V5l12-2v13"/>
    <circle cx="6" cy="18" r="3"/>
    <circle cx="18" cy="16" r="3"/>
  </svg>
);

export const DownloadIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  className = ""
}) => (
  <svg
    width={width}
    height={height}
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="7,10 12,15 17,10"/>
    <line x1="12" y1="15" x2="12" y2="3"/>
  </svg>
);

export const SettingsIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  className = ""
}) => (
  <svg
    width={width}
    height={height}
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <circle cx="12" cy="12" r="3"/>
    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 1 0 8 4 4 0 0 1 0-8z"/>
  </svg>
);

export const TikTokIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  className = ""
}) => (
  <svg
    width={width}
    height={height}
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M12.525 2.001a11.001 11.001 0 0 0-1.001 21.998v-9.997h-3.004V12.5c.001-2.002 1.002-4.003 3.004-4.003h.999V5.002c0-1.001.999-2.001 1.999-2.001h.001zm-1.001 1.999c-.552 0-1 .448-1 1v2.5h-1.5c-1.379 0-2.5 1.121-2.5 2.5v2.5h2.5v6.5c0 .552.448 1 1 1s1-.448 1-1v-6.5h2.5v-2.5c0-1.379-1.121-2.5-2.5-2.5h-1.5V5.002c0-.552-.448-1-1-1z"/>
  </svg>
);
