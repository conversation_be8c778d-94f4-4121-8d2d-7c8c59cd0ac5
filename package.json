{"name": "services-downloader-mp3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules && rimraf package-lock.json && rimraf yarn.lock && rimraf pnpm-lock.yaml && pnpm install"}, "dependencies": {"@imput/youtubei.js": "^14.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tobyg74/tiktok-api-dl": "^1.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "ffmpeg-static": "^5.2.0", "hls-parser": "^0.13.6", "lucide-react": "^0.525.0", "mime": "^4.0.7", "nanoid": "^5.1.5", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^6.0.1", "set-cookie-parser": "^2.7.1", "soundcloud.ts": "^0.6.5", "tailwind-merge": "^3.3.1", "undici": "^7.11.0", "yt-dlp-static": "^1.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24.0.14", "@types/react": "^19", "@types/react-dom": "^19", "@types/wicg-file-system-access": "^2023.10.6", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}